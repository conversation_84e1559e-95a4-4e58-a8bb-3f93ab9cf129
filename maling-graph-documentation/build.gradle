description = 'Maling Graph Documentation - 说明书生成器'

dependencies {
    // 依赖核心模块
    api project(':maling-graph-base')
    api project(':maling-graph-repository')
    api project(':maling-graph-ai')

    // Spring Boot 和 Spring AI
    implementation 'org.springframework.boot:spring-boot-starter:3.2.0'
    implementation 'org.springframework.ai:spring-ai-openai-spring-boot-starter:1.0.0-M3'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc:3.2.0'

    // MyBatis
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.3'

    // MySQL
    implementation 'mysql:mysql-connector-java:8.0.33'

    // JSON处理
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.15.2'

    // Apache Commons
    implementation 'org.apache.commons:commons-lang3:3.12.0'
    implementation 'org.apache.commons:commons-collections4:4.4'

    // Lombok
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"

    // Logging
    implementation "ch.qos.logback:logback-classic:${logbackVersion}"

    // Test dependencies
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
}
