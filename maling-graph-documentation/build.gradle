description = 'Maling Graph Documentation - 说明书生成器'

dependencies {
    // 依赖核心模块
    api project(':maling-graph-base')
    api project(':maling-graph-repository')
    api project(':maling-graph-ai')

    // 数据库支持
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa:3.2.0'
    implementation 'com.h2database:h2:2.2.224'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc:3.2.0'

    // JSON处理
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.15.2'

    // Apache Commons
    implementation 'org.apache.commons:commons-lang3:3.12.0'
    implementation 'org.apache.commons:commons-collections4:4.4'

    // Lombok
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"

    // Logging
    implementation "ch.qos.logback:logback-classic:${logbackVersion}"

    // Test dependencies
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
}
